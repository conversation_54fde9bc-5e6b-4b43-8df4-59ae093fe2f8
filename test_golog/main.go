package main

import (
	"errors"
	"fmt"

	"github.com/real-rm/golog"
)

func main() {
	fmt.Println("=== 测试 golog Error 函数对不同错误类型的处理 ===\n")

	// Test 1: fmt.Errorf
	err1 := fmt.Errorf("this is a test error with details: %s", "some detail")
	fmt.Println("1. 测试 fmt.Errorf:")
	golog.Error("Processing error", "error", err1)

	// Test 2: errors.New
	err2 := errors.New("simple error message")
	fmt.Println("\n2. 测试 errors.New:")
	golog.Error("Simple error test", "error", err2)

	// Test 3: custom error
	err3 := &customError{msg: "custom error message"}
	fmt.Println("\n3. 测试自定义错误:")
	golog.Error("Custom error test", "error", err3)

	// Test 4: nil error
	var err4 error
	fmt.Println("\n4. 测试 nil 错误:")
	golog.Error("Nil error test", "error", err4)

	// Test 5: wrapped error
	baseErr := errors.New("base error")
	wrappedErr := fmt.Errorf("wrapped: %w", baseErr)
	fmt.Println("\n5. 测试包装错误:")
	golog.Error("Wrapped error test", "error", wrappedErr)

	// Test 6: regular string (should still work)
	fmt.Println("\n6. 测试普通字符串:")
	golog.Error("String test", "message", "this is just a string")

	// Test 7: multiple parameters
	fmt.Println("\n7. 测试多个参数:")
	golog.Error("Multiple params", "error", err1, "code", 500, "user", "admin")

	fmt.Println("\n=== 测试完成 ===")
}

type customError struct {
	msg string
}

func (e *customError) Error() string {
	return e.msg
}
